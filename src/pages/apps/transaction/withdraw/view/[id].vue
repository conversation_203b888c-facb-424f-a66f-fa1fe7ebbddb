<script setup>
import { $apiAuth } from '@/utils/api'

const route = useRoute('apps-transaction-withdraw-view-id')

// const { data: userData } = await useApi(`/apps/transaction/deposit/${ route.params.id }`)
const { data: userData } = await $apiAuth(`/api/users/get/withdraw/transaction/${ route.params.id }`)
console.log(userData?.amountToPay)

const paymentInfo = [
  {
    avatarIcon: 'tabler-building-bank',
    avatarColor: 'success',
    title: 'Price',
    subtitle: 'Original Price',
    stats: `${userData?.price}`,
    profit: true,
  },
  {
    avatarIcon: 'tabler-calendar-plus',
    avatarColor: 'secondary',
    title: 'Created At',
    subtitle: 'Transaction Created Time',
    stats: `${userData?.createdAt}`,
    profit: false,
  },
  {
    avatarIcon: 'tabler-id',
    avatarColor: 'warning',
    title: 'Payment ID',
    subtitle: 'Unique Payment Identifier',
    stats: `${userData?.paymentId}`,
    profit: false,
  },
]


const timestamps = [
  {
    avatarIcon: 'tabler-message',
    avatarColor: 'primary',
    title: 'Settlement Message',
    subtitle: 'Message from settlement check',
    stats: `${userData?.isStMsg}`,
    profit: false,
  },
  {
    avatarIcon: 'tabler-alert-circle',
    avatarColor: 'error',
    title: 'Callback Message',
    subtitle: 'HTTP response from callback',
    stats: `${userData?.isCbMsg}`,
    profit: false,
  },
  {
    avatarIcon: 'tabler-send',
    avatarColor: 'success',
    title: 'Sent At',
    subtitle: 'Time when callback was sent',
    stats: `${userData?.sendAt}`,
    profit: false,
  },
  {
    avatarIcon: 'tabler-clock-check',
    avatarColor: 'secondary',
    title: 'Done At',
    subtitle: 'Time when the process was completed',
    stats: `${userData?.isDoneAt}`,
    profit: true,
  },
]


const transitions = [
  {
    avatarIcon: 'tabler-link',
    avatarColor: 'primary',
    title: 'Callback URL',
    subtitle: 'Destination for callback response',
    stats: `${userData?.callbackUrl}`,
    profit: false,
  },
  {
    avatarIcon: 'tabler-send',
    avatarColor: 'info',
    title: 'Send Status',
    subtitle: 'Status of the sending process',
    stats: `${userData?.sendStatus || '-'}`,
    profit: false,
  },
  {
    avatarIcon: 'tabler-message-chatbot',
    avatarColor: 'info',
    title: 'Send Message',
    subtitle: 'Message from sending process',
    stats: `${userData?.sendMessage || '-'}`,
    profit: false,
  },
  {
    avatarIcon: 'tabler-check',
    avatarColor: 'success',
    title: 'Settlement Check',
    subtitle: 'Settlement verification status',
    stats: `${userData?.isStCheck}`,
    profit: true,
  },
  {
    avatarIcon: 'tabler-x',
    avatarColor: 'error',
    title: 'Callback Check',
    subtitle: 'Callback verification status',
    stats: `${userData?.isCbCheck}`,
    profit: userData?.isCbCheck === 'Success', // ถ้า Success = true สีเขียว
  },
]

const bankInfo = [
  {
    avatarIcon: 'tabler-coins',
    avatarColor: 'primary',
    title: 'Bank Key',
    subtitle: 'Payment channel or coin type',
    stats: `${userData?.ref?.bankkey}`, // "usdt"
    profit: false,
  },
  {
    avatarIcon: 'tabler-wallet',
    avatarColor: 'success',
    title: 'Bank No',
    subtitle: 'Wallet address or bank number',
    stats: `${userData?.ref?.bankno}`, // "TWFe17f8gDYL82FDYC6MmjxT31bzSdqQP1"
    profit: false,
  },
  {
    avatarIcon: 'tabler-hash',
    avatarColor: 'info',
    title: 'Ref 1',
    subtitle: 'Custom reference 1',
    stats: `${userData?.ref?.ref1}`, // "data1"
    profit: false,
  },
  {
    avatarIcon: 'tabler-hash',
    avatarColor: 'info',
    title: 'Ref 2',
    subtitle: 'Custom reference 2',
    stats: `${userData?.ref?.ref2}`, // "data2"
    profit: false,
  },
]



</script>

<template>
  <!-- {{ userData }} -->
  <!-- 👉 Header  -->
  <div v-if="userData" class="d-flex justify-space-between align-center flex-wrap gap-y-4 mb-6">
    <div>
      <h1 class="text-h5 mb-1">
        Payment ID #{{ route.params.id }}
      </h1>
      <div class="text-body-1">
        {{ userData.createdAt }}
      </div>
    </div>
    <div class="d-flex gap-4">
      <VBtn
        variant="tonal"
        color="error"
      >
        Delete Customer
      </VBtn>
    </div>
  </div>

  <div v-else>
    <VAlert
      type="error"
      variant="tonal"
    >
      Invoice with ID  {{ route.params.id }} not found! Test
    </VAlert>
  </div>

  <div class="d-flex mb-6">
    <VRow class="match-height">
      <!-- 👉 Earning Reports -->
      <VCol
        cols="12"
        sm="6"
        lg="4"
      >
        <VCard
          title="Payment & Metadata Info"
          subtitle="Transaction data including amount"
        >
          <VCardText>
            <VList class="card-list">
              <VListItem
                v-for="paymentInfo in paymentInfo"
                :key="paymentInfo.title"
              >
                <template #prepend>
                  <VAvatar
                    size="34"
                    :color="paymentInfo.avatarColor"
                    variant="tonal"
                    class="me-1"
                    rounded
                  >
                    <VIcon
                      :icon="paymentInfo.avatarIcon"
                      size="22"
                    />
                  </VAvatar>
                </template>

                <VListItemTitle>
                  {{ paymentInfo.title }}
                </VListItemTitle>
                <VListItemSubtitle>
                  {{ paymentInfo.subtitle }}
                </VListItemSubtitle>

                <template #append>
                  <div class="d-flex align-center">
                    <span :class="`${paymentInfo.profit ? 'text-success' : 'text-error'} font-weight-medium`">{{ paymentInfo.stats }}</span>
                  </div>
                </template>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>

      <!-- 👉 timestamps -->
      <VCol
        cols="12"
        sm="6"
        lg="4"
      >
        <VCard
          title="Response Messages & Timestamps"
          subtitle="Message details and timestamps for sending, processing, and expiration."
        >
          <VCardText>
            <VList class="card-list">
              <VListItem
                v-for="timestamps in timestamps"
                :key="timestamps.title"
              >
                <template #prepend>
                  <VAvatar
                    size="34"
                    :color="timestamps.avatarColor"
                    variant="tonal"
                    class="me-1"
                    rounded
                  >
                    <VIcon
                      :icon="timestamps.avatarIcon"
                      size="22"
                    />
                  </VAvatar>
                </template>

                <VListItemTitle>
                  {{ timestamps.title }}
                </VListItemTitle>
                <VListItemSubtitle>
                  {{ timestamps.subtitle }}
                </VListItemSubtitle>

                <template #append>
                  <div class="d-flex align-center">
                    <span :class="`${timestamps.profit ? 'text-success' : 'text-error'} font-weight-medium`">{{ timestamps.stats }}</span>
                  </div>
                </template>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>

      <!-- 👉 Order -->
      <VCol
        cols="12"
        sm="6"
        lg="4"
      >
        <VCard
          title="Callback & Processing Status"
          subtitle="Status of callback, settlement, and related messages and flags."
        >
          <VCardText>
            <VList class="card-list">
              <VListItem
                v-for="transition in transitions"
                :key="transition.title"
              >
                <template #prepend>
                  <VAvatar
                    size="34"
                    :color="transition.avatarColor"
                    variant="tonal"
                    class="me-1"
                    rounded
                  >
                    <VIcon
                      :icon="transition.avatarIcon"
                      size="22"
                    />
                  </VAvatar>
                </template>

                <VListItemTitle>
                  {{ transition.title }}
                </VListItemTitle>
                <VListItemSubtitle>
                  {{ transition.subtitle }}
                </VListItemSubtitle>

                <template #append>
                  <div class="d-flex align-center">
                    <span :class="`${transition.profit ? 'text-success' : 'text-error'} font-weight-medium`">{{ transition.stats }}</span>
                  </div>
                </template>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>

      <!-- 👉 bankInfo -->
      <VCol
        cols="12"
        sm="6"
        lg="4"
      >
        <VCard
          title="Callback & Processing Status"
          subtitle="Status of callback, settlement, and related messages and flags."
        >
          <VCardText>
            <VList class="card-list">
              <VListItem
                v-for="bankInfo in bankInfo"
                :key="bankInfo.title"
              >
                <template #prepend>
                  <VAvatar
                    size="34"
                    :color="bankInfo.avatarColor"
                    variant="tonal"
                    class="me-1"
                    rounded
                  >
                    <VIcon
                      :icon="bankInfo.avatarIcon"
                      size="22"
                    />
                  </VAvatar>
                </template>

                <VListItemTitle>
                  {{ bankInfo.title }}
                </VListItemTitle>
                <VListItemSubtitle>
                  {{ bankInfo.subtitle }}
                </VListItemSubtitle>

                <template #append>
                  <div class="d-flex align-center">
                    <span :class="`${bankInfo.profit ? 'text-success' : 'text-error'} font-weight-medium`">{{ bankInfo.stats }}</span>
                  </div>
                </template>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>

<style lang="scss" scoped>
.card-list {
  --v-card-list-gap: 16px;
}
</style>
